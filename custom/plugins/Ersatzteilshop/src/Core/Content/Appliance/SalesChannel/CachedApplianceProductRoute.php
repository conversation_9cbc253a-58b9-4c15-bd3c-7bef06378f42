<?php declare(strict_types=1);

namespace Ersatzteilshop\Core\Content\Appliance\SalesChannel;

use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Request;
use Shopware\Core\Content\Product\ProductEntity;
use Ersatzteilshop\Core\Content\Appliance\ApplianceEntity;
use Shopware\Core\Framework\Adapter\Cache\CacheCompressor;
use Shopware\Core\System\SalesChannel\SalesChannelContext;
use Symfony\Component\Cache\Adapter\TagAwareAdapterInterface;
use Ersatzteilshop\Core\Content\Appliance\ApplianceCollection;
use Shopware\Core\Framework\Adapter\Cache\AbstractCacheTracer;
use Shopware\Core\Framework\DataAbstractionLayer\Search\IdSearchResult;
use Shopware\Core\Framework\DataAbstractionLayer\Search\EntitySearchResult;
use Shopware\Core\Content\Product\SalesChannel\Listing\ProductListingResult;
use Shopware\Core\Framework\DataAbstractionLayer\Cache\EntityCacheKeyGenerator;
use Shopware\Core\Framework\DataAbstractionLayer\FieldSerializer\JsonFieldSerializer;
use Shopware\Core\Content\Product\Aggregate\ProductManufacturer\ProductManufacturerCollection;

class CachedApplianceProductRoute extends AbstractApplianceProductRoute
{
    private AbstractApplianceProductRoute $decorated;

    private TagAwareAdapterInterface $cache;

    private EntityCacheKeyGenerator $generator;

    private AbstractCacheTracer $tracer;

    private LoggerInterface $logger;

    public function __construct(
        AbstractApplianceProductRoute $decorated,
        TagAwareAdapterInterface $cache,
        EntityCacheKeyGenerator $generator,
        AbstractCacheTracer $tracer,
        LoggerInterface $logger
    ) {
        $this->decorated = $decorated;
        $this->cache = $cache;
        $this->generator = $generator;
        $this->tracer = $tracer;
        $this->logger = $logger;
    }

    public function getDecorated(): AbstractApplianceProductRoute
    {
        return $this->decorated;
    }

    public function loadProducts(
        ApplianceEntity $appliance,
        Request $request,
        SalesChannelContext $salesChannelContext
    ): ProductListingResult {
        $item = $this->cache->getItem(
            $this->generateKey($appliance->getId(), $request, $salesChannelContext)
        );

        try {
            if ($item->isHit() && $item->get()) {
                $this->logger->info('cache-hit: ' . self::buildName($appliance->getId()));

                return CacheCompressor::uncompress($item);
            }
        } catch (\Throwable $e) {
            $this->logger->error($e->getMessage());
        }

        $this->logger->info('cache-miss: ' . self::buildName($appliance->getId()));

        $name = self::buildName($appliance->getId());

        $response = $this->tracer->trace(
            $name,
            function () use ($appliance, $request, $salesChannelContext) {
                return $this->getDecorated()->loadProducts($appliance, $request, $salesChannelContext);
            }
        );

        if (empty($response->count())) {
            return $response;
        }

        $item = CacheCompressor::compress($item, $response);

        $item->tag($this->generateProductTags($appliance->getId(), $response));

        $this->cache->save($item);

        return $response;
    }

    public function countProducts(
        ApplianceEntity $appliance,
        Request $request,
        SalesChannelContext $salesChannelContext
    ): int {
        $item = $this->cache->getItem(
            $this->generateKeyProductCount($appliance->getId(), $request, $salesChannelContext)
        );

        try {
            if ($item->isHit() && $item->get()) {
                $this->logger->info('cache-hit: ' . self::buildNameProductCount($appliance->getId()));

                return CacheCompressor::uncompress($item);
            }
        } catch (\Throwable $e) {
            $this->logger->error($e->getMessage());
        }

        $this->logger->info('cache-miss: ' . self::buildNameProductCount($appliance->getId()));

        $name = self::buildNameProductCount($appliance->getId());

        $count = $this->tracer->trace(
            $name,
            function () use ($appliance, $request, $salesChannelContext) {
                return $this->getDecorated()->countProducts($appliance, $request, $salesChannelContext);
            }
        );

        if (0 === $count) {
            return $count;
        }

        $item = CacheCompressor::compress($item, $count);

        $item->tag($this->generateProductTags($appliance->getId()));

        $this->cache->save($item);

        return $count;
    }

    public function loadAllFromProduct(
        string $productId,
        SalesChannelContext $salesChannelContext
    ): array {
        $item = $this->cache->getItem(
            $this->generateKeyAllAppliancesLoadedFromProduct($productId, $salesChannelContext)
        );

        try {
            if ($item->isHit() && $item->get()) {
                $this->logger->info('cache-hit: ' . self::buildNameAllAppliancesLoadFromProduct($productId));

                return CacheCompressor::uncompress($item);
            }
        } catch (\Throwable $e) {
            $this->logger->error($e->getMessage());
        }

        $this->logger->info('cache-miss: ' . self::buildNameAllAppliancesLoadFromProduct($productId));

        $name = self::buildNameAllAppliancesLoadFromProduct($productId);

        $response = $this->tracer->trace(
            $name,
            function () use ($productId, $salesChannelContext) {
                return $this->getDecorated()->loadAllFromProduct($productId, $salesChannelContext);
            }
        );

        $item = CacheCompressor::compress($item, $response);

        $item->tag([self::buildNameAllAppliancesLoadFromProduct($productId)]);

        $this->cache->save($item);

        return $response;
    }

    public function loadFromProductWithPagination(
        string              $productId,
        Request             $request,
        SalesChannelContext $salesChannelContext
    ): ApplianceCollection {
        return $this->decorated->loadFromProductWithPagination($productId, $request, $salesChannelContext);
    }

    public function loadManufacturersFromProduct(
        string              $productId,
        SalesChannelContext $salesChannelContext
    ): ProductManufacturerCollection {
        return $this->decorated->loadManufacturersFromProduct($productId, $salesChannelContext);
    }

    public static function buildName(string $id): string
    {
        return 'appliance-detail-load-products-' . $id;
    }

    public static function buildNameProductCount(string $id): string
    {
        return 'appliance-detail-load-product-count-' . $id;
    }

    public static function buildNameProducts(string $productId): string
    {
        return 'loaded-products-on-appliance-detail-' . $productId;
    }

    public static function buildNameAllAppliancesLoadFromProduct(string $productId): string
    {
        return 'all-appliances-loaded-from-product-' . $productId;
    }

    private function generateKey(string $id, Request $request, SalesChannelContext $salesChannelContext): string
    {
        return md5(JsonFieldSerializer::encodeJson([
            self::buildName($id),
            $request->query->all(),
            $this->generator->getSalesChannelContextHash($salesChannelContext),
            $salesChannelContext->getContext()->getExtensions()
        ]));
    }

    private function generateKeyProductCount(string $id, Request $request, SalesChannelContext $salesChannelContext): string
    {
        return md5(JsonFieldSerializer::encodeJson([
            self::buildNameProductCount($id),
            $request->query->all(),
            $this->generator->getSalesChannelContextHash($salesChannelContext),
            $salesChannelContext->getContext()->getExtensions()
        ]));
    }

    private function generateKeyAllAppliancesLoadedFromProduct(
        string $productId,
        SalesChannelContext $salesChannelContext
    ): string {
        return md5(JsonFieldSerializer::encodeJson([
            self::buildNameAllAppliancesLoadFromProduct($productId),
            $this->generator->getSalesChannelContextHash($salesChannelContext),
            $salesChannelContext->getContext()->getExtensions()
        ]));
    }

    private function generateProductTags(string $id, $result = null): array
    {
        $productIds = [];
        if ($result instanceof EntitySearchResult
            || $result instanceof IdSearchResult) {
            $productIds = array_values($result->getIds());
        }

        $tags = array_merge(
            $this->tracer->get(self::buildName($id)),
            [self::buildName($id)],
            array_map([$this, 'buildNameProducts'], $productIds),
            array_map([$this, 'buildNameProductCount'], $productIds),
        );

        return array_unique(array_filter($tags));
    }

    public function loadProductsByCategory(
        ApplianceEntity     $appliance,
        string              $categoryId,
        Request             $request,
        SalesChannelContext $salesChannelContext
    ): array
    {
        $key = $this->generateKeyCategoryProducts($appliance->getId(), $categoryId, $request, $salesChannelContext);
        $item = $this->cache->getItem($key);

        try {
            if ($item->isHit() && $item->get()) {
                $this->logger->info('cache-hit: category-products-' . $appliance->getId() . '-' . $categoryId);
                return CacheCompressor::uncompress($item);
            }
        } catch (\Throwable $e) {
            $this->logger->error($e->getMessage());
        }

        $this->logger->info('cache-miss: category-products-' . $appliance->getId() . '-' . $categoryId);

        $response = $this->getDecorated()->loadProductsByCategory($appliance, $categoryId, $request, $salesChannelContext);

        if (empty($response['products'])) {
            return $response;
        }

        $item = CacheCompressor::compress($item, $response);
        $item->tag([
            self::buildName($appliance->getId()),
            'appliance-category-' . $categoryId
        ]);

        $this->cache->save($item);

        return $response;
    }

    public function loadSearchFilteredProducts(
        ApplianceEntity     $appliance,
        Request             $request,
        SalesChannelContext $salesChannelContext
    ): ProductListingResult
    {
        $key = $this->generateKeySearchFilteredProducts($appliance->getId(), $request, $salesChannelContext);
        $item = $this->cache->getItem($key);

        try {
            if ($item->isHit() && $item->get()) {
                $this->logger->info('cache-hit: search-filtered-products-' . $appliance->getId());
                
                return CacheCompressor::uncompress($item);
            }
        } catch (\Throwable $e) {
            $this->logger->error($e->getMessage());
        }

        $this->logger->info('cache-miss: search-filtered-products-' . $appliance->getId());

        $result = $this->tracer->trace(self::buildName($appliance->getId()), function () use ($appliance, $request, $salesChannelContext) {
            return $this->getDecorated()->loadSearchFilteredProducts($appliance, $request, $salesChannelContext);
        });

        $item = CacheCompressor::compress($item, $result);
        $item->tag($this->generateProductTags($appliance->getId(), $result));
        
        if ($request->get('search')) {
            $item->tag('appliance-search-' . md5($request->get('search')));
        }

        $this->cache->save($item);

        return $result;
    }

    private function generateKeySearchFilteredProducts(
        string $applianceId,
        Request $request,
        SalesChannelContext $salesChannelContext
    ): string {
        return md5(JsonFieldSerializer::encodeJson([
            'search-filtered-products-' . $applianceId,
            $request->get('search', ''),
            $request->query->all(),
            $this->generator->getSalesChannelContextHash($salesChannelContext),
            $salesChannelContext->getContext()->getExtensions()
        ]));
    }

    private function generateKeyCategoryProducts(
        string $applianceId,
        string $categoryId,
        Request $request,
        SalesChannelContext $salesChannelContext
    ): string {
        return md5(JsonFieldSerializer::encodeJson([
            'appliance-category-products',
            $applianceId,
            $categoryId,
            $request->get('p', 1),
            $request->get('limit', 24),
            $this->generator->getSalesChannelContextHash($salesChannelContext),
        ]));
    }
}
