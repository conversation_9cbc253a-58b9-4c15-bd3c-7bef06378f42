<?php declare(strict_types=1);

namespace Ersatzteilshop\Core\Content\Appliance\SalesChannel;

use Symfony\Component\HttpFoundation\Request;
use Ersatzteilshop\Core\Content\Appliance\ApplianceEntity;
use Shopware\Core\System\SalesChannel\SalesChannelContext;
use Ersatzteilshop\Core\Content\Appliance\ApplianceCollection;
use Shopware\Core\Content\Product\SalesChannel\Listing\ProductListingResult;
use Shopware\Core\Content\Product\Aggregate\ProductManufacturer\ProductManufacturerCollection;

abstract class AbstractApplianceProductRoute
{
    public const TOTAL_ASWO_PRODUCT = 'totalAswo';

    protected const ASWO_PRODUCT_PER_PAGE = 10;

    public const MAX_MANUFACTURER = 22;

    abstract public function getDecorated(): AbstractApplianceProductRoute;

    abstract public function loadProducts(
        ApplianceEntity     $appliance,
        Request             $request,
        SalesChannelContext $salesChannelContext
    ): ProductListingResult;

    abstract public function countProducts(
        ApplianceEntity     $appliance,
        Request             $request,
        SalesChannelContext $salesChannelContext
    ): int;

    abstract public function loadFromProductWithPagination(
        string $productId,
        Request $request,
        SalesChannelContext $salesChannelContext
    ): ApplianceCollection;

    abstract public function loadAllFromProduct(
        string              $productId,
        SalesChannelContext $salesChannelContext
    ): array;

    abstract public function loadManufacturersFromProduct(
        string              $productId,
        SalesChannelContext $salesChannelContext
    ): ProductManufacturerCollection;

    abstract public function loadProductsByCategory(
        ApplianceEntity     $appliance,
        string              $categoryId,
        Request             $request,
        SalesChannelContext $salesChannelContext
    ): array;

    abstract public function loadSearchFilteredProducts(
        ApplianceEntity     $appliance,
        Request             $request,
        SalesChannelContext $salesChannelContext
    ): ProductListingResult;
}
