<?php
namespace XantenAswo\Service;

use K11\AswoEedClient\Interfaces\ProvidesLogging;
use Psr\Log\LoggerInterface;

class AswoCloudLogger implements ProvidesLogging
{
    private const MAX_VALUE_LENGTH = 10240; //gcp erlaubt 256kb pro Log Eintrag...

    private LoggerInterface $logger;

    public function __construct(LoggerInterface $logger)
    {
        $this->logger = $logger;
    }

    public function logApiInteraction(
        string $method,
        string $url,
        array $parameters,
        ?int $statusCode,
        ?string $content,
        float $executionTime,
        ?\Throwable $exception = null
    ): void {
        $logData = [
            'request' => [
                'method' => $method,
                'url' => $url,
                'parameters' => $this->formatData($parameters),
            ],
            'response' => [
                'statusCode' => $statusCode,
                'content' => $statusCode ? $this->formatData(json_decode($content, true) ?: $content) : null,
            ],
            'executionTime' => $executionTime,
        ];

        if ($exception) {
            $logData['error'] = [
                'message' => $exception->getMessage(),
                'code' => $exception->getCode(),
                'type' => get_class($exception),
            ];
        }

        $art = $logData['request']['parameters']['art'] ?? '';
        $this->logger->info("ASWO API Interaction - Art: {$art}", $logData);
    }

    private function formatData($data, int $depth = 0)
    {
        if ($depth > 10) {
            return '[Max Depth Reached]';
        }

        if (is_array($data)) {
            $formatted = [];
            foreach ($data as $key => $value) {
                $formatted[$key] = $this->formatData($value, $depth + 1);
            }
            return $formatted;
        }

        if (is_string($data) && strlen($data) > self::MAX_VALUE_LENGTH) {
            return substr($data, 0, self::MAX_VALUE_LENGTH) . '... [truncated]';
        }

        return $data;
    }
}
